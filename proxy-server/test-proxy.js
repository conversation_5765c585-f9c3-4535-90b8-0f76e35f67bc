#!/usr/bin/env node

/**
 * Test script for YouTube Proxy Server
 * Usage: node test-proxy.js [proxy-url]
 * Example: node test-proxy.js https://your-app.railway.app
 */

const baseUrl = process.argv[2] || 'http://localhost:3002';
const testVideoId = 'vrvEsNLhlag';

async function testEndpoint(url, description) {
  try {
    console.log(`\n🔍 Testing: ${description}`);
    console.log(`📍 URL: ${url}`);
    
    const startTime = Date.now();
    const response = await fetch(url);
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Success (${response.status}) - ${responseTime}ms`);
      
      if (data.languages) {
        console.log(`📊 Languages found: ${data.languages.length}`);
        console.log(`🎬 Video title: ${data.title}`);
        console.log(`🔄 Method: ${data.method || 'unknown'}`);
      } else {
        console.log(`📊 Response:`, JSON.stringify(data, null, 2));
      }
    } else {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      console.log(`❌ Failed (${response.status}) - ${responseTime}ms`);
      console.log(`📊 Error:`, JSON.stringify(errorData, null, 2));
    }
    
    return { success: response.ok, status: response.status, responseTime, data: response.ok ? data : errorData };
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runProxyTests() {
  console.log(`🚀 Testing YouTube Proxy Server at: ${baseUrl}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  
  const tests = [
    {
      url: `${baseUrl}/health`,
      description: 'Health Check'
    },
    {
      url: `${baseUrl}/api/subtitles/languages/${testVideoId}`,
      description: 'Subtitle Languages Extraction'
    }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testEndpoint(test.url, test.description);
    results.push({ ...test, ...result });
    
    // Wait between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📋 Test Summary:');
  console.log('================');
  
  let passed = 0;
  let failed = 0;
  let totalResponseTime = 0;
  
  results.forEach((result, index) => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const timing = result.responseTime ? ` (${result.responseTime}ms)` : '';
    console.log(`${index + 1}. ${result.description}: ${status}${timing}`);
    
    if (result.success) {
      passed++;
      if (result.responseTime) totalResponseTime += result.responseTime;
    } else {
      failed++;
      console.log(`   Error: ${result.error || result.data?.error || 'Unknown error'}`);
    }
  });
  
  const avgResponseTime = passed > 0 ? Math.round(totalResponseTime / passed) : 0;
  
  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`);
  console.log(`⚡ Average response time: ${avgResponseTime}ms`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Proxy server is working correctly.');
    
    // Additional success info
    const languageResult = results.find(r => r.description.includes('Subtitle'));
    if (languageResult && languageResult.data && languageResult.data.languages) {
      console.log(`\n🌍 Language Detection:`);
      console.log(`   Total languages: ${languageResult.data.languages.length}`);
      console.log(`   Video title: ${languageResult.data.title}`);
      console.log(`   Proxy method: ${languageResult.data.method || 'unknown'}`);
      
      // Show first few languages
      const firstLanguages = languageResult.data.languages.slice(0, 5);
      console.log(`   Sample languages: ${firstLanguages.map(l => l.name).join(', ')}...`);
    }
    
    process.exit(0);
  } else {
    console.log('💥 Some tests failed. Check the proxy server configuration.');
    process.exit(1);
  }
}

// Performance test
async function performanceTest() {
  console.log('\n🏃‍♂️ Running performance test...');
  
  const testUrl = `${baseUrl}/api/subtitles/languages/${testVideoId}`;
  const iterations = 3;
  const times = [];
  
  for (let i = 1; i <= iterations; i++) {
    console.log(`   Test ${i}/${iterations}...`);
    const startTime = Date.now();
    
    try {
      const response = await fetch(testUrl);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      if (response.ok) {
        times.push(responseTime);
        console.log(`   ✅ ${responseTime}ms`);
      } else {
        console.log(`   ❌ Failed (${response.status})`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    // Wait between tests
    if (i < iterations) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  if (times.length > 0) {
    const avgTime = Math.round(times.reduce((a, b) => a + b, 0) / times.length);
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`\n📈 Performance Results:`);
    console.log(`   Average: ${avgTime}ms`);
    console.log(`   Fastest: ${minTime}ms`);
    console.log(`   Slowest: ${maxTime}ms`);
    console.log(`   Success rate: ${times.length}/${iterations} (${Math.round(times.length/iterations*100)}%)`);
  }
}

// Run tests
runProxyTests()
  .then(() => performanceTest())
  .catch(error => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
