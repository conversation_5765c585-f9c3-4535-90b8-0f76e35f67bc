#!/usr/bin/env node

/**
 * YouTube Proxy Server - Advanced Bot Detection Bypass
 * 
 * This proxy server acts as an intermediary between your Vercel app and YouTube
 * to avoid bot detection by using different hosting, IPs, and stealth techniques.
 * 
 * Deploy this on: Railway, Render, DigitalOcean, AWS EC2, or any VPS
 */

import express from 'express';
import cors from 'cors';
import { createProxyMiddleware } from 'http-proxy-middleware';

const app = express();
const PORT = process.env.PORT || 3002;

// Enable CORS for all origins
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(express.json());

// User-Agent rotation pool
const USER_AGENTS = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
  'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
];

// Referer rotation pool
const REFERERS = [
  'https://www.google.com/',
  'https://www.bing.com/',
  'https://duckduckgo.com/',
  'https://www.youtube.com/',
  'https://www.reddit.com/',
  'https://twitter.com/',
  'https://www.facebook.com/'
];

// Helper function to get language name from code
const getLanguageName = (code) => {
  const languageMap = {
    'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German', 'it': 'Italian',
    'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese', 'ko': 'Korean', 'zh': 'Chinese',
    'ar': 'Arabic', 'hi': 'Hindi', 'tr': 'Turkish', 'pl': 'Polish', 'nl': 'Dutch',
    'sv': 'Swedish', 'da': 'Danish', 'no': 'Norwegian', 'fi': 'Finnish', 'cs': 'Czech',
    'hu': 'Hungarian', 'ro': 'Romanian', 'bg': 'Bulgarian', 'hr': 'Croatian', 'sk': 'Slovak',
    'sl': 'Slovenian', 'et': 'Estonian', 'lv': 'Latvian', 'lt': 'Lithuanian', 'uk': 'Ukrainian',
    'el': 'Greek', 'he': 'Hebrew', 'th': 'Thai', 'vi': 'Vietnamese', 'id': 'Indonesian',
    'ms': 'Malay', 'tl': 'Filipino', 'sw': 'Swahili', 'af': 'Afrikaans'
  };
  return languageMap[code] || code.toUpperCase();
};

// Advanced stealth request function
const makeStealthRequest = async (url, options = {}) => {
  const randomUA = USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
  const randomReferer = REFERERS[Math.floor(Math.random() * REFERERS.length)];
  
  const headers = {
    'User-Agent': randomUA,
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.9,es;q=0.8,fr;q=0.7',
    'Accept-Encoding': 'gzip, deflate, br',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    'Sec-Ch-Ua-Mobile': randomUA.includes('Mobile') ? '?1' : '?0',
    'Sec-Ch-Ua-Platform': randomUA.includes('Windows') ? '"Windows"' : randomUA.includes('Mac') ? '"macOS"' : '"Linux"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'Dnt': '1',
    'Connection': 'keep-alive',
    'Referer': randomReferer,
    ...options.headers
  };

  console.log(`[Proxy] Making request to: ${url}`);
  console.log(`[Proxy] Using UA: ${randomUA.substring(0, 50)}...`);

  return fetch(url, {
    ...options,
    headers
  });
};

// Session simulation with cookie persistence
const simulateSession = async (videoId) => {
  console.log(`[Proxy] Starting session simulation for ${videoId}`);
  
  try {
    // Step 1: Visit YouTube homepage
    console.log(`[Proxy] Step 1: Visiting YouTube homepage`);
    const homeResponse = await makeStealthRequest('https://www.youtube.com/');
    
    if (!homeResponse.ok) {
      console.log(`[Proxy] Homepage failed: ${homeResponse.status}`);
    }

    // Extract cookies
    const cookies = homeResponse.headers.get('set-cookie') || '';
    console.log(`[Proxy] Cookies extracted: ${cookies ? 'Yes' : 'No'}`);

    // Human-like delay
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));

    // Step 2: Visit video page with session
    console.log(`[Proxy] Step 2: Visiting video page`);
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
    
    const videoResponse = await makeStealthRequest(videoUrl, {
      headers: {
        'Cookie': cookies,
        'Referer': 'https://www.youtube.com/',
        'Sec-Fetch-Site': 'same-origin'
      }
    });

    if (!videoResponse.ok) {
      throw new Error(`Video request failed: ${videoResponse.status} ${videoResponse.statusText}`);
    }

    const html = await videoResponse.text();
    console.log(`[Proxy] Video page fetched successfully (${html.length} chars)`);
    
    return html;

  } catch (error) {
    console.error(`[Proxy] Session simulation failed: ${error.message}`);
    throw error;
  }
};

// Multiple endpoint strategy
const tryMultipleEndpoints = async (videoId) => {
  const endpoints = [
    { url: `https://www.youtube.com/watch?v=${videoId}`, type: 'desktop' },
    { url: `https://m.youtube.com/watch?v=${videoId}`, type: 'mobile' },
    { url: `https://www.youtube.com/embed/${videoId}`, type: 'embed' },
    { url: `https://youtube.com/watch?v=${videoId}`, type: 'alt' }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`[Proxy] Trying ${endpoint.type}: ${endpoint.url}`);
      
      const isMobile = endpoint.type === 'mobile';
      const isEmbed = endpoint.type === 'embed';
      
      const response = await makeStealthRequest(endpoint.url, {
        headers: {
          'User-Agent': isMobile 
            ? 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
            : USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)],
          'Sec-Fetch-Dest': isEmbed ? 'iframe' : 'document',
          'Sec-Fetch-Site': isEmbed ? 'cross-site' : 'none'
        }
      });

      if (response.ok) {
        const html = await response.text();
        console.log(`[Proxy] Success with ${endpoint.type}`);
        return html;
      }

      console.log(`[Proxy] Failed ${endpoint.type}: ${response.status}`);

    } catch (error) {
      console.log(`[Proxy] Error with ${endpoint.type}: ${error.message}`);
    }

    // Delay between attempts
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  throw new Error('All endpoints failed');
};

// Extract subtitle languages from HTML
const extractSubtitleLanguages = (html, videoId) => {
  console.log(`[Proxy] Extracting subtitle data for ${videoId}`);

  // Extract video title
  let videoTitle = 'YouTube Video';
  const titleMatch = html.match(/<title>([^<]+)<\/title>/) || html.match(/"title":"([^"]+)"/);
  if (titleMatch && titleMatch[1]) {
    videoTitle = titleMatch[1].replace(' - YouTube', '').replace(/\\u[\dA-F]{4}/gi, '').trim();
  }

  // Extract player response with multiple patterns
  let playerResponseMatch = html.match(/var ytInitialPlayerResponse = ({.+?});/) ||
                           html.match(/ytInitialPlayerResponse":\s*({.+?}),"ytInitialData"/) ||
                           html.match(/"ytInitialPlayerResponse":({.+?}),"ytInitialData"/);

  if (!playerResponseMatch) {
    throw new Error('Could not find player response data');
  }

  const playerResponse = JSON.parse(playerResponseMatch[1]);
  const availableLanguages = [];

  // Extract subtitle tracks
  if (playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
    const captionTracks = playerResponse.captions.playerCaptionsTracklistRenderer.captionTracks;

    captionTracks.forEach(track => {
      const langCode = track.languageCode;
      const langName = track.name?.simpleText || getLanguageName(langCode);
      const isAutoGenerated = track.kind === 'asr';

      availableLanguages.push({
        code: langCode,
        name: langName,
        isAutoGenerated: isAutoGenerated
      });
    });
  }

  if (availableLanguages.length === 0) {
    throw new Error('No subtitles available for this video');
  }

  console.log(`[Proxy] Extracted ${availableLanguages.length} languages`);

  return {
    videoId,
    title: videoTitle,
    thumbnail: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
    languages: availableLanguages,
    proxy: true,
    timestamp: new Date().toISOString()
  };
};

// Main proxy endpoint for subtitle languages
app.get('/api/subtitles/languages/:videoId', async (req, res) => {
  const { videoId } = req.params;
  
  if (!videoId) {
    return res.status(400).json({ error: 'Video ID is required' });
  }

  console.log(`[Proxy] Processing request for video: ${videoId}`);

  try {
    let html = '';

    // Strategy 1: Try session simulation first
    try {
      html = await simulateSession(videoId);
    } catch (sessionError) {
      console.log(`[Proxy] Session simulation failed: ${sessionError.message}`);
      
      // Strategy 2: Try multiple endpoints
      html = await tryMultipleEndpoints(videoId);
    }

    // Extract and return subtitle data
    const result = extractSubtitleLanguages(html, videoId);
    res.json(result);

  } catch (error) {
    console.error(`[Proxy] Error processing ${videoId}:`, error.message);

    if (error.message.includes('HTTP 429')) {
      res.status(429).json({
        error: 'Rate limited by YouTube. Please try again later.',
        proxy: true
      });
    } else if (error.message.includes('HTTP 403') || error.message.includes('bot')) {
      res.status(503).json({
        error: 'YouTube access restricted. Proxy may need IP rotation.',
        proxy: true
      });
    } else {
      res.status(500).json({
        error: 'Proxy server error',
        details: error.message,
        proxy: true
      });
    }
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    service: 'YouTube Proxy Server',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Generic proxy endpoint for any YouTube URL
app.get('/proxy', async (req, res) => {
  const { url } = req.query;
  
  if (!url || !url.includes('youtube.com')) {
    return res.status(400).json({ error: 'Valid YouTube URL required' });
  }

  try {
    const response = await makeStealthRequest(url);
    const html = await response.text();
    
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  } catch (error) {
    res.status(500).json({ error: 'Proxy request failed', details: error.message });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 YouTube Proxy Server running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/health`);
  console.log(`🎯 API endpoint: http://localhost:${PORT}/api/subtitles/languages/{videoId}`);
  console.log(`🔄 Generic proxy: http://localhost:${PORT}/proxy?url={youtubeUrl}`);
});
