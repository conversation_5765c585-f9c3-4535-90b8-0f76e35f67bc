{"name": "youtube-proxy-server", "version": "1.0.0", "description": "Advanced proxy server to bypass YouTube bot detection", "main": "server.js", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "node test-proxy.js"}, "keywords": ["youtube", "proxy", "bot-detection", "bypass", "subtitles"], "author": "YouTube Subtitle Extractor", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "http-proxy-middleware": "^2.0.6"}, "devDependencies": {"nodemon": "^3.0.2"}}