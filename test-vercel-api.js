#!/usr/bin/env node

/**
 * Test script for Vercel API endpoints
 * Usage: node test-vercel-api.js [base-url]
 * Example: node test-vercel-api.js https://your-app.vercel.app
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);

const baseUrl = process.argv[2] || 'http://localhost:3001';
const testVideoId = 'vrvEsNLhlag'; // Test video ID

async function testEndpoint(url, description) {
  try {
    console.log(`\n🔍 Testing: ${description}`);
    console.log(`📍 URL: ${url}`);
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ Success (${response.status})`);
      console.log(`📊 Response:`, JSON.stringify(data, null, 2));
    } else {
      console.log(`❌ Failed (${response.status})`);
      console.log(`📊 Error:`, JSON.stringify(data, null, 2));
    }
    
    return { success: response.ok, status: response.status, data };
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log(`🚀 Testing Vercel API endpoints at: ${baseUrl}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
  
  const tests = [
    {
      url: `${baseUrl}/api/health`,
      description: 'Health Check'
    },
    {
      url: `${baseUrl}/api/subtitles/languages/${testVideoId}`,
      description: 'Subtitle Languages'
    }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testEndpoint(test.url, test.description);
    results.push({ ...test, ...result });
    
    // Wait between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📋 Test Summary:');
  console.log('================');
  
  let passed = 0;
  let failed = 0;
  
  results.forEach((result, index) => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${index + 1}. ${result.description}: ${status}`);
    
    if (result.success) {
      passed++;
    } else {
      failed++;
      console.log(`   Error: ${result.error || result.data?.error || 'Unknown error'}`);
    }
  });
  
  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('💥 Some tests failed. Check the logs above for details.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
