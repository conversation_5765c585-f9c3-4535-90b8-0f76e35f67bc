// Using direct HTTP requests to avoid bot detection

// Helper function to get language name from code
const getLanguageName = (code) => {
  const languageMap = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'tr': 'Turkish',
    'pl': 'Polish',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'da': 'Danish',
    'no': 'Norwegian',
    'fi': 'Finnish',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'ro': 'Romanian',
    'bg': 'Bulgarian',
    'hr': 'Croatian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'et': 'Estonian',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'uk': 'Ukrainian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Filipino',
    'sw': 'Swahili',
    'af': 'Afrikaans'
  };
  
  return languageMap[code] || code.toUpperCase();
};

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { videoId } = req.query;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`[Vercel] Fetching subtitle languages for video: ${videoId}`);

    // Use direct HTTP request to get video page and extract subtitle info
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;

    const response = await fetch(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
      }
    });
    console.log(`[Vercel] Response: ${response}`);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();
    console.log(`[Vercel] HTML: ${html}`);

    // Extract video title
    const titleMatch = html.match(/<title>([^<]+)<\/title>/);
    const videoTitle = titleMatch ? titleMatch[1].replace(' - YouTube', '').trim() : 'YouTube Video';
    console.log(`[Vercel] Video title: ${videoTitle}`);

    // Extract player response data
    const playerResponseMatch = html.match(/var ytInitialPlayerResponse = ({.+?});/);
    if (!playerResponseMatch) {
      throw new Error('Could not find player response data');
    }
    console.log(`[Vercel] Player response: ${playerResponseMatch[1]}`);

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const availableLanguages = [];

    // Get available subtitle tracks
    if (playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      const captionTracks = playerResponse.captions.playerCaptionsTracklistRenderer.captionTracks;

      captionTracks.forEach(track => {
        const langCode = track.languageCode;
        const langName = track.name?.simpleText || getLanguageName(langCode);
        const isAutoGenerated = track.kind === 'asr';

        availableLanguages.push({
          code: langCode,
          name: langName,
          isAutoGenerated: isAutoGenerated
        });
      });
    }

    if (availableLanguages.length === 0) {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    res.status(200).json({
      videoId,
      title: videoTitle,
      thumbnail: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      languages: availableLanguages
    });

  } catch (error) {
    console.error('[Vercel] Error fetching subtitle languages:', error);
    console.error('[Vercel] Error stack:', error.stack);

    // Handle specific error cases
    if (error.message.includes('HTTP 429')) {
      res.status(429).json({
        error: 'Rate limited by YouTube. Please try again later.',
        details: 'Too many requests',
        environment: 'vercel'
      });
    } else if (error.message.includes('HTTP 403')) {
      res.status(403).json({
        error: 'Access denied by YouTube. Video may be private or restricted.',
        details: 'Forbidden',
        environment: 'vercel'
      });
    } else if (error.message.includes('HTTP 404')) {
      res.status(404).json({
        error: 'Video not found. Please check the video ID.',
        details: 'Not found',
        environment: 'vercel'
      });
    } else {
      res.status(500).json({
        error: 'Failed to fetch subtitle languages',
        details: error instanceof Error ? error.message : 'Unknown error',
        environment: 'vercel',
        timestamp: new Date().toISOString()
      });
    }
  }
};
