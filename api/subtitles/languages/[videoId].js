// Using direct HTTP requests to avoid bot detection

// Helper function to get language name from code
const getLanguageName = (code) => {
  const languageMap = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'tr': 'Turkish',
    'pl': 'Polish',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'da': 'Danish',
    'no': 'Norwegian',
    'fi': 'Finnish',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'ro': 'Romanian',
    'bg': 'Bulgarian',
    'hr': 'Croatian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'et': 'Estonian',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'uk': 'Ukrainian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Filipino',
    'sw': 'Swahili',
    'af': 'Afrikaans'
  };
  
  return languageMap[code] || code.toUpperCase();
};

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { videoId } = req.query;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`[Vercel] Fetching subtitle languages for video: ${videoId}`);

    // Try multiple approaches to avoid bot detection
    let html = '';
    let videoTitle = 'YouTube Video';

    // Approach 1: Try embed page first (less likely to be blocked)
    try {
      const embedUrl = `https://www.youtube.com/embed/${videoId}`;
      console.log(`[Vercel] Trying embed URL: ${embedUrl}`);

      const embedResponse = await fetch(embedUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Referer': 'https://www.youtube.com/',
          'Sec-Fetch-Dest': 'iframe',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'same-origin'
        }
      });

      if (embedResponse.ok) {
        html = await embedResponse.text();
        console.log(`[Vercel] Successfully fetched embed page`);
      } else {
        throw new Error(`Embed failed: ${embedResponse.status}`);
      }
    } catch (embedError) {
      console.log(`[Vercel] Embed approach failed: ${embedError.message}`);

      // Approach 2: Try regular watch page with enhanced headers
      const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
      console.log(`[Vercel] Trying watch URL: ${videoUrl}`);

      const response = await fetch(videoUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
          'Sec-Ch-Ua-Mobile': '?0',
          'Sec-Ch-Ua-Platform': '"macOS"',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-User': '?1',
          'Upgrade-Insecure-Requests': '1',
          'Referer': 'https://www.google.com/'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      html = await response.text();
      console.log(`[Vercel] Successfully fetched watch page`);
    }

    // Extract video title
    const titleMatch = html.match(/<title>([^<]+)<\/title>/);
    if (titleMatch && titleMatch[1]) {
      videoTitle = titleMatch[1].replace(' - YouTube', '').trim();
    }
    console.log(`[Vercel] Video title: ${videoTitle}`);

    // Extract player response data
    const playerResponseMatch = html.match(/var ytInitialPlayerResponse = ({.+?});/);
    if (!playerResponseMatch) {
      throw new Error('Could not find player response data');
    }
    console.log(`[Vercel] Player response: ${playerResponseMatch[1]}`);

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    const availableLanguages = [];

    // Get available subtitle tracks
    if (playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      const captionTracks = playerResponse.captions.playerCaptionsTracklistRenderer.captionTracks;

      captionTracks.forEach(track => {
        const langCode = track.languageCode;
        const langName = track.name?.simpleText || getLanguageName(langCode);
        const isAutoGenerated = track.kind === 'asr';

        availableLanguages.push({
          code: langCode,
          name: langName,
          isAutoGenerated: isAutoGenerated
        });
      });
    }

    if (availableLanguages.length === 0) {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    res.status(200).json({
      videoId,
      title: videoTitle,
      thumbnail: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      languages: availableLanguages
    });

  } catch (error) {
    console.error('[Vercel] Error fetching subtitle languages:', error);
    console.error('[Vercel] Error stack:', error.stack);

    // Handle specific error cases
    if (error.message.includes('HTTP 429')) {
      res.status(429).json({
        error: 'Rate limited by YouTube. Please try again later.',
        details: 'Too many requests',
        environment: 'vercel'
      });
    } else if (error.message.includes('HTTP 403')) {
      res.status(403).json({
        error: 'Access denied by YouTube. Video may be private or restricted.',
        details: 'Forbidden',
        environment: 'vercel'
      });
    } else if (error.message.includes('HTTP 404')) {
      res.status(404).json({
        error: 'Video not found. Please check the video ID.',
        details: 'Not found',
        environment: 'vercel'
      });
    } else if (error.message.includes('bot') || error.message.includes('Sign in')) {
      res.status(503).json({
        error: 'YouTube is blocking automated requests. This is a temporary issue.',
        details: 'Bot detection triggered',
        environment: 'vercel',
        suggestion: 'Try again in a few minutes'
      });
    } else {
      res.status(500).json({
        error: 'Failed to fetch subtitle languages',
        details: error instanceof Error ? error.message : 'Unknown error',
        environment: 'vercel',
        timestamp: new Date().toISOString()
      });
    }
  }
};
