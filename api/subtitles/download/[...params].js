// Using direct HTTP requests to avoid bot detection

// Helper function to download and parse subtitle content
const downloadAndParseSubtitles = async (subtitleUrl) => {
  try {
    const response = await fetch(subtitleUrl);
    const content = await response.text();
    
    // Parse VTT format
    if (content.includes('WEBVTT')) {
      return parseVTTContent(content);
    }
    
    // Try to parse as JSON3 format (YouTube's format)
    try {
      const jsonData = JSON.parse(content);
      if (jsonData.events) {
        return parseJSON3Content(jsonData);
      }
    } catch (e) {
      // Not JSON, continue with VTT parsing
    }
    
    // Fallback to VTT parsing
    return parseVTTContent(content);
  } catch (error) {
    console.error('Error downloading/parsing subtitles:', error);
    return [];
  }
};

// Parse VTT subtitle format
const parseVTTContent = (content) => {
  const subtitles = [];
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Look for timestamp lines (format: 00:00:00.000 --> 00:00:03.000)
    if (line.includes('-->')) {
      const [startTime, endTime] = line.split('-->').map(t => t.trim());
      const start = parseVTTTime(startTime);
      const end = parseVTTTime(endTime);
      
      // Get the text lines that follow
      const textLines = [];
      i++; // Move to next line after timestamp
      
      while (i < lines.length && lines[i].trim() !== '' && !lines[i].includes('-->')) {
        const textLine = lines[i].trim();
        if (textLine) {
          // Clean up any VTT formatting tags
          const cleanText = textLine.replace(/<[^>]*>/g, '').trim();
          if (cleanText) {
            textLines.push(cleanText);
          }
        }
        i++;
      }
      i--; // Step back one since the loop will increment
      
      if (textLines.length > 0) {
        subtitles.push({
          start,
          end,
          text: textLines.join(' ')
        });
      }
    }
  }
  
  return subtitles;
};

// Parse JSON3 subtitle format (YouTube's format)
const parseJSON3Content = (jsonData) => {
  const subtitles = [];
  
  if (jsonData.events) {
    for (const event of jsonData.events) {
      if (event.segs) {
        let text = '';
        for (const seg of event.segs) {
          if (seg.utf8) {
            text += seg.utf8;
          }
        }
        
        if (text.trim()) {
          subtitles.push({
            start: event.tStartMs / 1000,
            end: (event.tStartMs + event.dDurationMs) / 1000,
            text: text.trim()
          });
        }
      }
    }
  }
  
  return subtitles;
};

// Parse VTT time format to seconds
const parseVTTTime = (timeStr) => {
  const parts = timeStr.split(':');
  if (parts.length === 3) {
    const hours = parseInt(parts[0]);
    const minutes = parseInt(parts[1]);
    const seconds = parseFloat(parts[2]);
    return hours * 3600 + minutes * 60 + seconds;
  }
  return 0;
};

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { params } = req.query;
    const [videoId, langCode] = params;
    
    if (!videoId || !langCode) {
      return res.status(400).json({ error: 'Video ID and language code are required' });
    }

    console.log(`Downloading subtitles for video: ${videoId}, language: ${langCode}`);

    // Use direct HTTP request to get video page and extract subtitle info
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;

    const response = await fetch(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"macOS"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://www.google.com/',
        'Origin': 'https://www.youtube.com'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();

    // Extract video title
    const titleMatch = html.match(/<title>([^<]+)<\/title>/);
    const videoTitle = titleMatch ? titleMatch[1].replace(' - YouTube', '').trim() : 'YouTube Video';

    // Extract player response data
    const playerResponseMatch = html.match(/var ytInitialPlayerResponse = ({.+?});/);
    if (!playerResponseMatch) {
      throw new Error('Could not find player response data');
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    let subtitles = [];
    let subtitleUrl = null;

    // Find the subtitle track for the requested language
    if (playerResponse?.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      const captionTracks = playerResponse.captions.playerCaptionsTracklistRenderer.captionTracks;

      const track = captionTracks.find(track => track.languageCode === langCode);

      if (track) {
        subtitleUrl = track.baseUrl;
        console.log(`Found subtitles for ${langCode}`);
      } else {
        return res.status(404).json({ error: 'Subtitles not found for the specified language' });
      }
    } else {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    // Download and parse subtitles
    if (subtitleUrl) {
      subtitles = await downloadAndParseSubtitles(subtitleUrl);
    }

    res.status(200).json({
      videoId,
      title: videoTitle,
      thumbnail: `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      language: langCode,
      subtitles
    });

  } catch (error) {
    console.error('Error downloading subtitles:', error);

    // Handle specific error cases
    if (error.message.includes('HTTP 429')) {
      res.status(429).json({
        error: 'Rate limited by YouTube. Please try again later.',
        details: 'Too many requests'
      });
    } else if (error.message.includes('HTTP 403')) {
      res.status(403).json({
        error: 'Access denied by YouTube. Video may be private or restricted.',
        details: 'Forbidden'
      });
    } else if (error.message.includes('HTTP 404')) {
      res.status(404).json({
        error: 'Video not found. Please check the video ID.',
        details: 'Not found'
      });
    } else {
      res.status(500).json({
        error: 'Failed to download subtitles',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
};
