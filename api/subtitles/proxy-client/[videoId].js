// Vercel API that uses external proxy server to bypass YouTube bot detection

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { videoId } = req.query;
    
    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`[Vercel-Proxy] Fetching subtitle languages for video: ${videoId}`);

    // Multiple proxy server URLs (deploy to different platforms for redundancy)
    const PROXY_SERVERS = [
      process.env.PROXY_SERVER_1, // Primary proxy (Railway)
      process.env.PROXY_SERVER_2, // Secondary proxy (Render)
      process.env.PROXY_SERVER_3, // Tertiary proxy (DigitalOcean)
      'https://your-proxy-server.railway.app', // Fallback URLs
      'https://your-proxy-server.onrender.com'
    ].filter(Boolean); // Remove undefined values

    let lastError = null;

    // Try each proxy server
    for (const proxyUrl of PROXY_SERVERS) {
      try {
        console.log(`[Vercel-Proxy] Trying proxy: ${proxyUrl}`);
        
        const proxyResponse = await fetch(`${proxyUrl}/api/subtitles/languages/${videoId}`, {
          method: 'GET',
          headers: {
            'User-Agent': 'Vercel-Function/1.0',
            'Accept': 'application/json',
            'X-Forwarded-For': req.headers['x-forwarded-for'] || 'unknown'
          },
          timeout: 30000 // 30 second timeout
        });

        if (proxyResponse.ok) {
          const data = await proxyResponse.json();
          console.log(`[Vercel-Proxy] Success with proxy: ${proxyUrl}`);
          
          // Add proxy information to response
          data.proxyUsed = proxyUrl;
          data.method = 'external-proxy';
          
          return res.status(200).json(data);
        } else {
          const errorText = await proxyResponse.text();
          lastError = new Error(`Proxy ${proxyUrl} returned ${proxyResponse.status}: ${errorText}`);
          console.log(`[Vercel-Proxy] Proxy failed: ${lastError.message}`);
        }

      } catch (error) {
        lastError = error;
        console.log(`[Vercel-Proxy] Proxy error: ${error.message}`);
      }

      // Small delay between proxy attempts
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // All proxies failed
    throw lastError || new Error('All proxy servers failed');

  } catch (error) {
    console.error('[Vercel-Proxy] Error:', error);

    // Return appropriate error response
    if (error.message.includes('timeout')) {
      res.status(504).json({
        error: 'Proxy server timeout',
        details: 'Request took too long to process',
        suggestion: 'Try again in a moment'
      });
    } else if (error.message.includes('ECONNREFUSED') || error.message.includes('fetch failed')) {
      res.status(503).json({
        error: 'Proxy server unavailable',
        details: 'Cannot connect to proxy servers',
        suggestion: 'Proxy servers may be down for maintenance'
      });
    } else {
      res.status(500).json({
        error: 'Failed to fetch subtitle languages via proxy',
        details: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
};
